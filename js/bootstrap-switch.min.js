/* ========================================================================
 * bootstrap-switch - v3.0.0
 * http://www.bootstrap-switch.org
 * ========================================================================
 * Copyright 2012-2013 Mattia Larentis
 *
 * ========================================================================
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * ========================================================================
 */

(function(){var t=[].slice;!function(o,e){"use strict";var s;return s=function(){function t(t,e){var s;null==e&&(e={}),this.$element=o(t),this.options=o.extend({},o.fn.bootstrapSwitch.defaults,e,{state:this.$element.is(":checked"),size:this.$element.data("size"),animate:this.$element.data("animate"),disabled:this.$element.is(":disabled"),readonly:this.$element.is("[readonly]"),onColor:this.$element.data("on-color"),offColor:this.$element.data("off-color"),onText:this.$element.data("on-text"),offText:this.$element.data("off-text"),labelText:this.$element.data("label-text")}),this.$wrapper=o("<div>"),this.$container=o("<div>"),this.$on=o("<span>",{html:this.options.onText}),this.$off=o("<span>",{html:this.options.offText}),this.$label=o("<label>",{"for":this.$element.attr("id"),html:this.options.labelText}),s=function(t){return function(e){var s,i,n,a;if(!o.isArray(e))return""+t.options.baseClass+"-"+e;for(i=[],n=0,a=e.length;a>n;n++)s=e[n],i.push(""+t.options.baseClass+"-"+s);return i.join(" ")}}(this),this.$wrapper.addClass(function(t){return function(){var o;return o=[""+t.options.baseClass,""+t.options.baseClass+"-"+t.options.wrapperClass],o.push(t.options.state?""+t.options.baseClass+"-"+t.options.onModifierClass:""+t.options.baseClass+"-"+t.options.offModifierClass),null!=t.options.size&&o.push(""+t.options.baseClass+"-"+t.options.size),t.options.animate&&o.push(""+t.options.baseClass+"-"+t.options.animateModifierClass),t.options.disabled&&o.push(""+t.options.baseClass+"-"+t.options.disabledModifierClass),t.options.readonly&&o.push(""+t.options.baseClass+"-"+t.options.readonlyModifierClass),t.$element.attr("id")&&o.push(""+t.options.baseClass+"-id-"+t.$element.attr("id")),o.join(" ")}}(this)),this.$container.addClass(s(this.options.containerClass)),this.$on.addClass(""+s(this.options.handleOnClass)+" "+this.options.baseClass+"-"+this.options.onColor),this.$off.addClass(""+s(this.options.handleOffClass)+" "+this.options.baseClass+"-"+this.options.offColor),this.$label.addClass(s(this.options.labelClass)),this.$element.on("init.bootstrapSwitch",function(o){return function(){return o.options.onInit.apply(t,arguments)}}(this)),this.$element.on("switchChange.bootstrapSwitch",function(o){return function(){return o.options.onSwitchChange.apply(t,arguments)}}(this)),this.$container=this.$element.wrap(this.$container).parent(),this.$wrapper=this.$container.wrap(this.$wrapper).parent(),this.$element.before(this.$on).before(this.$label).before(this.$off).trigger("init.bootstrapSwitch"),this._elementHandlers(),this._handleHandlers(),this._labelHandlers(),this._formHandler()}return t.prototype.name="bootstrap-switch",t.prototype._constructor=t,t.prototype.state=function(t,o){return"undefined"==typeof t?this.options.state:this.options.disabled||this.options.readonly?this.$element:(t=!!t,this.$element.prop("checked",t).trigger("change.bootstrapSwitch",o),this.$element)},t.prototype.toggleState=function(t){return this.options.disabled||this.options.readonly?this.$element:this.$element.prop("checked",!this.options.state).trigger("change.bootstrapSwitch",t)},t.prototype.size=function(t){return"undefined"==typeof t?this.options.size:(null!=this.options.size&&this.$wrapper.removeClass(""+this.options.baseClass+"-"+this.options.size),this.$wrapper.addClass(""+this.options.baseClass+"-"+t),this.options.size=t,this.$element)},t.prototype.animate=function(t){return"undefined"==typeof t?this.options.animate:(t=!!t,this.$wrapper[t?"addClass":"removeClass"](""+this.options.baseClass+"-"+this.options.animateModifierClass),this.options.animate=t,this.$element)},t.prototype.disabled=function(t){return"undefined"==typeof t?this.options.disabled:(t=!!t,this.$wrapper[t?"addClass":"removeClass"](""+this.options.baseClass+"-"+this.options.disabledModifierClass),this.$element.prop("disabled",t),this.options.disabled=t,this.$element)},t.prototype.toggleDisabled=function(){return this.$element.prop("disabled",!this.options.disabled),this.$wrapper.toggleClass(""+this.options.baseClass+"-"+this.options.disabledModifierClass),this.options.disabled=!this.options.disabled,this.$element},t.prototype.readonly=function(t){return"undefined"==typeof t?this.options.readonly:(t=!!t,this.$wrapper[t?"addClass":"removeClass"](""+this.options.baseClass+"-"+this.options.readonlyModifierClass),this.$element.prop("readonly",t),this.options.readonly=t,this.$element)},t.prototype.toggleReadonly=function(){return this.$element.prop("readonly",!this.options.readonly),this.$wrapper.toggleClass(""+this.options.baseClass+"-"+this.options.readonlyModifierClass),this.options.readonly=!this.options.readonly,this.$element},t.prototype.onColor=function(t){var o;return o=this.options.onColor,"undefined"==typeof t?o:(null!=o&&this.$on.removeClass(""+this.options.baseClass+"-"+o),this.$on.addClass(""+this.options.baseClass+"-"+t),this.options.onColor=t,this.$element)},t.prototype.offColor=function(t){var o;return o=this.options.offColor,"undefined"==typeof t?o:(null!=o&&this.$off.removeClass(""+this.options.baseClass+"-"+o),this.$off.addClass(""+this.options.baseClass+"-"+t),this.options.offColor=t,this.$element)},t.prototype.onText=function(t){return"undefined"==typeof t?this.options.onText:(this.$on.html(t),this.options.onText=t,this.$element)},t.prototype.offText=function(t){return"undefined"==typeof t?this.options.offText:(this.$off.html(t),this.options.offText=t,this.$element)},t.prototype.labelText=function(t){return"undefined"==typeof t?this.options.labelText:(this.$label.html(t),this.options.labelText=t,this.$element)},t.prototype.destroy=function(){var t;return t=this.$element.closest("form"),t.length&&t.off("reset.bootstrapSwitch").removeData("bootstrap-switch"),this.$container.children().not(this.$element).remove(),this.$element.unwrap().unwrap().off(".bootstrapSwitch").removeData("bootstrap-switch"),this.$element},t.prototype._elementHandlers=function(){return this.$element.on({"change.bootstrapSwitch":function(t){return function(e,s){var i;return e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),i=t.$element.is(":checked"),i!==t.options.state?(t.options.state=i,t.$wrapper.removeClass(i?""+t.options.baseClass+"-"+t.options.offModifierClass:""+t.options.baseClass+"-"+t.options.onModifierClass).addClass(i?""+t.options.baseClass+"-"+t.options.onModifierClass:""+t.options.baseClass+"-"+t.options.offModifierClass),s?void 0:(t.$element.is(":radio")&&o("[name='"+t.$element.attr("name")+"']").not(t.$element).prop("checked",!1).trigger("change.bootstrapSwitch",!0),t.$element.trigger("switchChange.bootstrapSwitch",[i]))):void 0}}(this),"focus.bootstrapSwitch":function(t){return function(o){return o.preventDefault(),o.stopPropagation(),o.stopImmediatePropagation(),t.$wrapper.addClass(""+t.options.baseClass+"-"+t.options.focusedModifierClass)}}(this),"blur.bootstrapSwitch":function(t){return function(o){return o.preventDefault(),o.stopPropagation(),o.stopImmediatePropagation(),t.$wrapper.removeClass(""+t.options.baseClass+"-"+t.options.focusedModifierClass)}}(this),"keydown.bootstrapSwitch":function(t){return function(o){if(o.which&&!t.options.disabled&&!t.options.readonly)switch(o.which){case 32:return o.preventDefault(),o.stopPropagation(),o.stopImmediatePropagation(),t.toggleState();case 37:return o.preventDefault(),o.stopPropagation(),o.stopImmediatePropagation(),t.state(!1);case 39:return o.preventDefault(),o.stopPropagation(),o.stopImmediatePropagation(),t.state(!0)}}}(this)})},t.prototype._handleHandlers=function(){return this.$on.on("click.bootstrapSwitch",function(t){return function(){return t.state(!1),t.$element.trigger("focus.bootstrapSwitch")}}(this)),this.$off.on("click.bootstrapSwitch",function(t){return function(){return t.state(!0),t.$element.trigger("focus.bootstrapSwitch")}}(this))},t.prototype._labelHandlers=function(){return this.$label.on({"mousemove.bootstrapSwitch touchmove.bootstrapSwitch":function(t){return function(o){var e,s,i;if(t.drag)return o.preventDefault(),s=((o.pageX||o.originalEvent.touches[0].pageX)-t.$wrapper.offset().left)/t.$wrapper.width()*100,e=25,i=75,e>s?s=e:s>i&&(s=i),t.$container.css("margin-left",""+(s-i)+"%"),t.$element.trigger("focus.bootstrapSwitch")}}(this),"mousedown.bootstrapSwitch touchstart.bootstrapSwitch":function(t){return function(o){return t.drag||t.options.disabled||t.options.readonly?void 0:(o.preventDefault(),t.drag=!0,t.options.animate&&t.$wrapper.removeClass(""+t.options.baseClass+"-"+t.options.animateModifierClass),t.$element.trigger("focus.bootstrapSwitch"))}}(this),"mouseup.bootstrapSwitch touchend.bootstrapSwitch":function(t){return function(o){return t.drag?(o.preventDefault(),t.drag=!1,t.$element.prop("checked",parseInt(t.$container.css("margin-left"),10)>-(t.$container.width()/6)).trigger("change.bootstrapSwitch"),t.$container.css("margin-left",""),t.options.animate?t.$wrapper.addClass(""+t.options.baseClass+"-"+t.options.animateModifierClass):void 0):void 0}}(this),"mouseleave.bootstrapSwitch":function(t){return function(){return t.$label.trigger("mouseup.bootstrapSwitch")}}(this)})},t.prototype._formHandler=function(){var t;return t=this.$element.closest("form"),t.data("bootstrap-switch")?void 0:t.on("reset.bootstrapSwitch",function(){return e.setTimeout(function(){return t.find("input").filter(function(){return o(this).data("bootstrap-switch")}).each(function(){return o(this).bootstrapSwitch("state",!1)})},1)}).data("bootstrap-switch",!0)},t}(),o.fn.bootstrapSwitch=function(){var e,i,n;return i=arguments[0],e=2<=arguments.length?t.call(arguments,1):[],n=this,this.each(function(){var t,a;return t=o(this),a=t.data("bootstrap-switch"),a||t.data("bootstrap-switch",a=new s(this,i)),"string"==typeof i?n=a[i].apply(a,e):void 0}),n},o.fn.bootstrapSwitch.Constructor=s,o.fn.bootstrapSwitch.defaults={state:!0,size:null,animate:!0,disabled:!1,readonly:!1,onColor:"primary",offColor:"default",onText:"ON",offText:"OFF",labelText:"&nbsp;",baseClass:"bootstrap-switch",wrapperClass:"wrapper",containerClass:"container",handleOnClass:"handle-on",handleOffClass:"handle-off",labelClass:"label",onModifierClass:"on",offModifierClass:"off",focusedModifierClass:"focused",animateModifierClass:"animate",disabledModifierClass:"disabled",readonlyModifierClass:"readonly",onInit:function(){},onSwitchChange:function(){}}}(window.jQuery,window)}).call(this);